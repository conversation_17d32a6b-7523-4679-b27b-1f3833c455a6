import java.io.*;
import java.util.*;

/**
 * InputParser - Utility class for reading and parsing input data files
 * Uses Scanner library for efficient parsing of dispatcher time and process data
 */
public class InputParser {
    
    /**
     * ParsedData - Inner class to encapsulate parsed input data
     * Contains dispatcher time and list of tasks
     */
    public static class ParsedData {
        private final int dispatcherTime;
        private final List<Task> tasks;
        
        public ParsedData(int dispatcherTime, List<Task> tasks) {
            this.dispatcherTime = dispatcherTime;
            this.tasks = new ArrayList<>(tasks);
        }
        
        public int getDispatcherTime() {
            return dispatcherTime;
        }
        
        public List<Task> getTasks() {
            return new ArrayList<>(tasks);
        }
        
        @Override
        public String toString() {
            return String.format("ParsedData{dispatcherTime=%d, tasks=%d}", 
                               dispatcherTime, tasks.size());
        }
    }
    
    /**
     * Parses input file using filename string
     * @param filename the name of the input file to parse
     * @return ParsedData containing dispatcher time and list of tasks
     * @throws FileNotFoundException if the file cannot be found
     * @throws IOException if there's an error reading the file
     * @throws IllegalArgumentException if the file format is invalid
     */
    public static ParsedData parseFile(String filename) throws IOException {
        return parseFile(new File(filename));
    }
    
    /**
     * Parses input file using File object
     * @param file the File object to parse
     * @return ParsedData containing dispatcher time and list of tasks
     * @throws FileNotFoundException if the file cannot be found
     * @throws IOException if there's an error reading the file
     * @throws IllegalArgumentException if the file format is invalid
     */
    public static ParsedData parseFile(File file) throws IOException {
        Scanner scanner = null;
        try {
            scanner = new Scanner(file);
            return parseInput(scanner);
        } finally {
            if (scanner != null) {
                scanner.close();
            }
        }
    }
    
    /**
     * Core parsing logic using Scanner
     * @param scanner the Scanner object to read from
     * @return ParsedData containing dispatcher time and list of tasks
     * @throws IllegalArgumentException if the file format is invalid
     */
    private static ParsedData parseInput(Scanner scanner) {
        int dispatcherTime = -1;
        List<Task> tasks = new ArrayList<>();
        
        // Skip to BEGIN marker
        while (scanner.hasNextLine()) {
            String line = scanner.nextLine().trim();
            if (line.equals("BEGIN")) {
                break;
            }
        }
        
        // Parse dispatcher time and tasks
        while (scanner.hasNextLine()) {
            String line = scanner.nextLine().trim();
            
            // Skip empty lines
            if (line.isEmpty()) {
                continue;
            }
            
            // Parse dispatcher time
            if (line.startsWith("DISP:")) {
                dispatcherTime = parseDispatcherTime(line);
            }
            // Parse task data
            else if (line.startsWith("PID:")) {
                Task task = parseTask(line, scanner);
                if (task != null) {
                    tasks.add(task);
                }
            }
            // End of file marker
            else if (line.equals("EOF")) {
                break;
            }
            // Skip END markers and other lines
        }
        
        // Validate parsed data
        if (dispatcherTime == -1) {
            throw new IllegalArgumentException("Dispatcher time not found in input file");
        }
        
        if (tasks.isEmpty()) {
            throw new IllegalArgumentException("No tasks found in input file");
        }
        
        // Sort tasks by arrival time and PID (using Task's compareTo method)
        Collections.sort(tasks);
        
        return new ParsedData(dispatcherTime, tasks);
    }
    
    /**
     * Parses the dispatcher time from a DISP: line
     * @param line the line containing dispatcher time (e.g., "DISP: 1")
     * @return the dispatcher time as an integer
     * @throws IllegalArgumentException if the format is invalid
     */
    private static int parseDispatcherTime(String line) {
        try {
            String timeStr = line.substring(5).trim(); // Remove "DISP:" prefix
            int time = Integer.parseInt(timeStr);
            if (time < 0) {
                throw new IllegalArgumentException("Dispatcher time cannot be negative: " + time);
            }
            return time;
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid dispatcher time format: " + line, e);
        } catch (StringIndexOutOfBoundsException e) {
            throw new IllegalArgumentException("Invalid dispatcher time line: " + line, e);
        }
    }
    
    /**
     * Parses a single task from PID:, ArrTime:, and SrvTime: lines
     * @param pidLine the line containing PID (e.g., "PID: p1")
     * @param scanner the Scanner to read additional lines
     * @return a Task object, or null if parsing fails
     * @throws IllegalArgumentException if the task format is invalid
     */
    private static Task parseTask(String pidLine, Scanner scanner) {
        try {
            // Parse PID
            String pid = pidLine.substring(4).trim(); // Remove "PID:" prefix
            if (pid.isEmpty()) {
                throw new IllegalArgumentException("Empty PID found");
            }
            
            // Parse arrival time
            if (!scanner.hasNextLine()) {
                throw new IllegalArgumentException("Missing arrival time for PID: " + pid);
            }
            String arrTimeLine = scanner.nextLine().trim();
            if (!arrTimeLine.startsWith("ArrTime:")) {
                throw new IllegalArgumentException("Expected ArrTime line, found: " + arrTimeLine);
            }
            int arrTime = Integer.parseInt(arrTimeLine.substring(8).trim());
            if (arrTime < 0) {
                throw new IllegalArgumentException("Arrival time cannot be negative: " + arrTime);
            }
            
            // Parse service time
            if (!scanner.hasNextLine()) {
                throw new IllegalArgumentException("Missing service time for PID: " + pid);
            }
            String srvTimeLine = scanner.nextLine().trim();
            if (!srvTimeLine.startsWith("SrvTime:")) {
                throw new IllegalArgumentException("Expected SrvTime line, found: " + srvTimeLine);
            }
            int srvTime = Integer.parseInt(srvTimeLine.substring(8).trim());
            if (srvTime <= 0) {
                throw new IllegalArgumentException("Service time must be positive: " + srvTime);
            }
            
            // Skip END line
            if (scanner.hasNextLine()) {
                String endLine = scanner.nextLine().trim();
                if (!endLine.equals("END")) {
                    // Put the line back by not consuming it, but we can't with Scanner
                    // So we'll just log a warning and continue
                    System.err.println("Warning: Expected END line after task, found: " + endLine);
                }
            }
            
            return new Task(pid, arrTime, srvTime);
            
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid number format in task data", e);
        } catch (StringIndexOutOfBoundsException e) {
            throw new IllegalArgumentException("Invalid task line format: " + pidLine, e);
        }
    }
    
    /**
     * Validates that tasks are ordered correctly (Pn arrives before or at same time as Pn+1)
     * @param tasks the list of tasks to validate
     * @throws IllegalArgumentException if tasks are not properly ordered
     */
    public static void validateTaskOrdering(List<Task> tasks) {
        for (int i = 1; i < tasks.size(); i++) {
            Task prev = tasks.get(i - 1);
            Task curr = tasks.get(i);
            
            if (prev.getArrTime() > curr.getArrTime()) {
                throw new IllegalArgumentException(
                    String.format("Task ordering violation: %s (arrival=%d) comes after %s (arrival=%d)",
                        prev.getPID(), prev.getArrTime(), curr.getPID(), curr.getArrTime()));
            }
        }
    }
}
