import java.util.*;

/**
 * Alg - Abstract base class for all scheduling algorithms
 * Defines the common interface that all scheduling algorithms must implement
 * Represents the 'Alg' abstract concept from the flowchart
 */
public abstract class Alg {
    
    protected List<Task> tasks;
    protected int dispatcherTime;
    protected SchedulingResult result;
    protected String algorithmName;
    
    /**
     * Constructor for the abstract algorithm class
     * @param tasks the list of tasks to be scheduled
     * @param dispatcherTime the dispatcher overhead time
     * @param algorithmName the name of the algorithm (e.g., "FCFS", "RR", "SRR", "FB")
     */
    public Alg(List<Task> tasks, int dispatcherTime, String algorithmName) {
        this.tasks = new ArrayList<>();
        // Create deep copies of tasks to avoid modifying original data
        for (Task task : tasks) {
            Task taskCopy = new Task(task.getPID(), task.getArrTime(), task.getSrvTime());
            this.tasks.add(taskCopy);
        }
        this.dispatcherTime = dispatcherTime;
        this.algorithmName = algorithmName;
        this.result = new SchedulingResult();
    }
    
    /**
     * Abstract method to execute the scheduling logic for the given set of tasks
     * Each concrete algorithm must implement this method with its specific scheduling logic
     * @return SchedulingResult containing the execution timeline and statistics
     */
    public abstract SchedulingResult run();
    
    /**
     * Abstract method to print detailed results for each process
     * Should output turnaround time and waiting time for each process
     * Format should match the expected output format
     */
    public abstract void outResults();
    
    /**
     * Abstract method to return or print summary statistics
     * Should provide average turnaround time and average waiting time
     * Used for contributing to the overall summary table
     * @return AlgorithmSummary containing average times for this algorithm
     */
    public abstract AlgorithmSummary outSummary();
    
    /**
     * Helper method to reset all tasks to their initial state
     * Calls the reset() method on each task
     */
    protected void resetTasks() {
        for (Task task : tasks) {
            task.reset();
        }
    }
    
    /**
     * Helper method to sort tasks by arrival time and PID (using Task's compareTo method)
     */
    protected void sortTasksByArrival() {
        Collections.sort(tasks);
    }
    
    /**
     * Helper method to calculate turnaround time for a task
     * Turnaround Time = Completion Time - Arrival Time
     * @param task the task to calculate turnaround time for
     * @param completionTime the time when the task completed
     * @return the turnaround time
     */
    protected int calculateTurnaroundTime(Task task, int completionTime) {
        return completionTime - task.getArrTime();
    }
    
    /**
     * Helper method to calculate waiting time for a task
     * Waiting Time = Turnaround Time - Service Time
     * @param turnaroundTime the turnaround time for the task
     * @param serviceTime the service time of the task
     * @return the waiting time
     */
    protected int calculateWaitingTime(int turnaroundTime, int serviceTime) {
        return turnaroundTime - serviceTime;
    }
    
    /**
     * Helper method to add a timeline entry to the result
     * @param time the time when the event occurred
     * @param taskPID the process ID of the task
     */
    protected void addTimelineEntry(int time, String taskPID) {
        result.addTimelineEntry("T" + time + ": " + taskPID);
    }
    
    /**
     * Gets the algorithm name
     * @return the name of the algorithm
     */
    public String getAlgorithmName() {
        return algorithmName;
    }
    
    /**
     * Gets the dispatcher time
     * @return the dispatcher overhead time
     */
    public int getDispatcherTime() {
        return dispatcherTime;
    }
    
    /**
     * Gets a copy of the tasks list
     * @return a copy of the tasks being scheduled
     */
    public List<Task> getTasks() {
        return new ArrayList<>(tasks);
    }
    
    /**
     * Gets the current scheduling result
     * @return the SchedulingResult object containing execution data
     */
    public SchedulingResult getResult() {
        return result;
    }
}

/**
 * Helper class to encapsulate algorithm summary statistics
 * Used by the outSummary() method to return average times
 */
class AlgorithmSummary {
    private String algorithmName;
    private double averageTurnaroundTime;
    private double averageWaitingTime;
    
    public AlgorithmSummary(String algorithmName, double averageTurnaroundTime, double averageWaitingTime) {
        this.algorithmName = algorithmName;
        this.averageTurnaroundTime = averageTurnaroundTime;
        this.averageWaitingTime = averageWaitingTime;
    }
    
    public String getAlgorithmName() {
        return algorithmName;
    }
    
    public double getAverageTurnaroundTime() {
        return averageTurnaroundTime;
    }
    
    public double getAverageWaitingTime() {
        return averageWaitingTime;
    }
    
    @Override
    public String toString() {
        return String.format("%-10s %-23.2f %-12.2f", 
            algorithmName, averageTurnaroundTime, averageWaitingTime);
    }
}
