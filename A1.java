import java.io.*;
import java.util.*;

/**
 * A1 - Main entry point for the scheduling simulation program
 * This class orchestrates the execution of four scheduling algorithms:
 * FCFS, RR, SRR, and FB
 */
public class A1 {
    
    private int dispatcherTime;
    private List<Task> tasks;
    
    public A1() {
        this.tasks = new ArrayList<>();
    }
    
    /**
     * Main method - entry point for the scheduling simulation
     * @param args command-line arguments, expects input filename
     */
    public static void main(String[] args) {
        if (args.length != 1) {
            System.err.println("Usage: java A1 <input_file>");
            System.exit(1);
        }
        
        A1 simulator = new A1();
        try {
            // Step 1: Read dispatcher time and process data from input file
            simulator.readInputFile(args[0]);
            
            // Step 2: Execute each scheduling algorithm sequentially
            simulator.runAllAlgorithms();
            
        } catch (FileNotFoundException e) {
            System.err.println("Error: Input file '" + args[0] + "' not found.");
            System.exit(1);
        } catch (IOException e) {
            System.err.println("Error reading input file: " + e.getMessage());
            System.exit(1);
        }
    }
    
    /**
     * Reads the input file and parses dispatcher time and task data
     * @param filename the input file name
     * @throws IOException if file reading fails
     */
    private void readInputFile(String filename) throws IOException {
        BufferedReader reader = new BufferedReader(new FileReader(filename));
        String line;
        
        while ((line = reader.readLine()) != null) {
            line = line.trim();
            
            if (line.equals("BEGIN")) {
                // Start of dispatcher section
                continue;
            } else if (line.startsWith("DISP:")) {
                // Parse dispatcher time
                dispatcherTime = Integer.parseInt(line.substring(5).trim());
            } else if (line.startsWith("PID:")) {
                // Parse task data
                String pid = line.substring(4).trim();
                
                // Read arrival time
                line = reader.readLine().trim();
                int arrTime = Integer.parseInt(line.substring(8).trim());
                
                // Read service time
                line = reader.readLine().trim();
                int srvTime = Integer.parseInt(line.substring(8).trim());
                
                // Create and add task
                Task task = new Task(pid, arrTime, srvTime);
                tasks.add(task);
                
                // Skip END line
                reader.readLine();
            } else if (line.equals("EOF")) {
                break;
            }
        }
        reader.close();
    }
    
    /**
     * Executes all four scheduling algorithms and prints results
     */
    private void runAllAlgorithms() {
        // Store results for summary table
        Map<String, Double> avgTurnaroundTimes = new HashMap<>();
        Map<String, Double> avgWaitingTimes = new HashMap<>();
        
        // Execute FCFS
        SchedulingResult fcfsResult = runFCFS();
        printAlgorithmResults("FCFS", fcfsResult);
        avgTurnaroundTimes.put("FCFS", fcfsResult.getAverageTurnaroundTime());
        avgWaitingTimes.put("FCFS", fcfsResult.getAverageWaitingTime());
        
        // Execute RR
        SchedulingResult rrResult = runRR();
        printAlgorithmResults("RR", rrResult);
        avgTurnaroundTimes.put("RR", rrResult.getAverageTurnaroundTime());
        avgWaitingTimes.put("RR", rrResult.getAverageWaitingTime());
        
        // Execute SRR
        SchedulingResult srrResult = runSRR();
        printAlgorithmResults("SRR", srrResult);
        avgTurnaroundTimes.put("SRR", srrResult.getAverageTurnaroundTime());
        avgWaitingTimes.put("SRR", srrResult.getAverageWaitingTime());
        
        // Execute FB
        SchedulingResult fbResult = runFB();
        printAlgorithmResults("FB", fbResult);
        avgTurnaroundTimes.put("FB", fbResult.getAverageTurnaroundTime());
        avgWaitingTimes.put("FB", fbResult.getAverageWaitingTime());
        
        // Print summary table
        printSummaryTable(avgTurnaroundTimes, avgWaitingTimes);
    }
    
    /**
     * Runs First Come First Served scheduling algorithm
     * @return SchedulingResult containing execution details
     */
    private SchedulingResult runFCFS() {
        // TODO: Implement FCFS algorithm
        // Reset all tasks before running algorithm
        for (Task task : tasks) {
            task.reset();
        }
        
        // Placeholder implementation
        return new SchedulingResult();
    }
    
    /**
     * Runs Round Robin scheduling algorithm
     * @return SchedulingResult containing execution details
     */
    private SchedulingResult runRR() {
        // TODO: Implement RR algorithm
        // Reset all tasks before running algorithm
        for (Task task : tasks) {
            task.reset();
        }
        
        // Placeholder implementation
        return new SchedulingResult();
    }
    
    /**
     * Runs Shortest Remaining Time scheduling algorithm
     * @return SchedulingResult containing execution details
     */
    private SchedulingResult runSRR() {
        // TODO: Implement SRR algorithm
        // Reset all tasks before running algorithm
        for (Task task : tasks) {
            task.reset();
        }
        
        // Placeholder implementation
        return new SchedulingResult();
    }
    
    /**
     * Runs Feedback scheduling algorithm
     * @return SchedulingResult containing execution details
     */
    private SchedulingResult runFB() {
        // TODO: Implement FB algorithm
        // Reset all tasks before running algorithm
        for (Task task : tasks) {
            task.reset();
        }
        
        // Placeholder implementation
        return new SchedulingResult();
    }
    
    /**
     * Prints the results for a specific algorithm
     * @param algorithmName the name of the algorithm
     * @param result the scheduling result
     */
    private void printAlgorithmResults(String algorithmName, SchedulingResult result) {
        System.out.println(algorithmName + ":");
        
        // Print execution timeline
        for (String timelineEntry : result.getTimeline()) {
            System.out.println(timelineEntry);
        }
        System.out.println();
        
        // Print process statistics table
        System.out.println("Process  Turnaround Time  Waiting Time");
        for (Task task : result.getProcessedTasks()) {
            System.out.printf("%-8s %-16d %-12d%n", 
                task.getPID(), 
                result.getTurnaroundTime(task.getPID()), 
                result.getWaitingTime(task.getPID()));
        }
        System.out.println();
    }
    
    /**
     * Prints the summary table comparing all algorithms
     * @param avgTurnaroundTimes map of algorithm names to average turnaround times
     * @param avgWaitingTimes map of algorithm names to average waiting times
     */
    private void printSummaryTable(Map<String, Double> avgTurnaroundTimes, Map<String, Double> avgWaitingTimes) {
        System.out.println("Summary");
        System.out.println("Algorithm  Average Turnaround Time  Waiting Time");
        
        String[] algorithms = {"FCFS", "RR", "SRR", "FB"};
        for (String algorithm : algorithms) {
            System.out.printf("%-10s %-23.2f %-12.2f%n", 
                algorithm, 
                avgTurnaroundTimes.get(algorithm), 
                avgWaitingTimes.get(algorithm));
        }
    }
    
    // Getters for testing purposes
    public int getDispatcherTime() {
        return dispatcherTime;
    }
    
    public List<Task> getTasks() {
        return new ArrayList<>(tasks);
    }
}
