import java.util.*;

/**
 * SchedulingResult - Helper class to store the results of a scheduling algorithm execution
 * Contains timeline events, processed tasks, and timing statistics
 */
public class SchedulingResult {
    private List<String> timeline;
    private List<Task> processedTasks;
    private Map<String, Integer> turnaroundTimes;
    private Map<String, Integer> waitingTimes;
    
    public SchedulingResult() {
        this.timeline = new ArrayList<>();
        this.processedTasks = new ArrayList<>();
        this.turnaroundTimes = new HashMap<>();
        this.waitingTimes = new HashMap<>();
    }
    
    /**
     * Adds a timeline entry (e.g., "T1: p1")
     * @param entry the timeline entry to add
     */
    public void addTimelineEntry(String entry) {
        timeline.add(entry);
    }
    
    /**
     * Adds a processed task to the result
     * @param task the task that was processed
     */
    public void addProcessedTask(Task task) {
        processedTasks.add(task);
    }
    
    /**
     * Sets the turnaround time for a specific process
     * @param pid the process ID
     * @param time the turnaround time
     */
    public void setTurnaroundTime(String pid, int time) {
        turnaroundTimes.put(pid, time);
    }
    
    /**
     * Sets the waiting time for a specific process
     * @param pid the process ID
     * @param time the waiting time
     */
    public void setWaitingTime(String pid, int time) {
        waitingTimes.put(pid, time);
    }
    
    /**
     * Gets the timeline entries
     * @return list of timeline entries
     */
    public List<String> getTimeline() {
        return new ArrayList<>(timeline);
    }
    
    /**
     * Gets the processed tasks
     * @return list of processed tasks
     */
    public List<Task> getProcessedTasks() {
        return new ArrayList<>(processedTasks);
    }
    
    /**
     * Gets the turnaround time for a specific process
     * @param pid the process ID
     * @return the turnaround time
     */
    public int getTurnaroundTime(String pid) {
        return turnaroundTimes.getOrDefault(pid, 0);
    }
    
    /**
     * Gets the waiting time for a specific process
     * @param pid the process ID
     * @return the waiting time
     */
    public int getWaitingTime(String pid) {
        return waitingTimes.getOrDefault(pid, 0);
    }
    
    /**
     * Calculates and returns the average turnaround time
     * @return the average turnaround time
     */
    public double getAverageTurnaroundTime() {
        if (turnaroundTimes.isEmpty()) {
            return 0.0;
        }
        
        int total = turnaroundTimes.values().stream().mapToInt(Integer::intValue).sum();
        return (double) total / turnaroundTimes.size();
    }
    
    /**
     * Calculates and returns the average waiting time
     * @return the average waiting time
     */
    public double getAverageWaitingTime() {
        if (waitingTimes.isEmpty()) {
            return 0.0;
        }
        
        int total = waitingTimes.values().stream().mapToInt(Integer::intValue).sum();
        return (double) total / waitingTimes.size();
    }
    
    /**
     * Clears all stored data
     */
    public void clear() {
        timeline.clear();
        processedTasks.clear();
        turnaroundTimes.clear();
        waitingTimes.clear();
    }
}
