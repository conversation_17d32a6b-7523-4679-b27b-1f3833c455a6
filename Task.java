public class Task implements Comparable<Task> {
    private String PID;
    private int ArrTime;
    private int SrvTime;
    private int workAT;
    private int workSt;
    
    // Constructor
    public Task(String PID, int ArrTime, int SrvTime, int workAT, int workSt) {
        this.PID = PID;
        this.ArrTime = ArrTime;
        this.SrvTime = SrvTime;
        this.workAT = workAT;
        this.workSt = workSt;
    }

    // Constructor with reinitialisation logic
    public Task(String pid, int arrt, int srvTime) {
        this.PID = pid;
        this.ArrTime = arrt;
        this.SrvTime = srvTime;
        this.workAT = arrt;
        this.workSt = srvTime;
    }
    
    // Getter methods
    public String getPID() {
        return PID;
    }
    
    public int getArrTime() {
        return ArrTime;
    }
    
    public int getSrvTime() {
        return SrvTime;
    }
    
    public int getWorkAT() {
        return workAT;
    }
    
    public int getWorkSt() {
        return workSt;
    }
    
    // Setter methods
    public void setPID(String PID) {
        this.PID = PID;
    }
    
    public void setArrTime(int ArrTime) {
        this.ArrTime = ArrTime;
    }
    
    public void setSrvTime(int SrvTime) {
        this.SrvTime = SrvTime;
    }
    
    public void setWorkAT(int workAT) {
        this.workAT = workAT;
    }
    
    public void setWorkSt(int workSt) {
        this.workSt = workSt;
    }

    // Reset method to reinitialise working values
    public void reset() {
        this.workAT = this.ArrTime;
        this.workSt = this.SrvTime;
    }

    // Lifecycle management methods
    public void arrive() {
        // TODO: Implement arrival logic
    }

    public void service() {
        // TODO: Implement service logic
    }

    // Implementation of Comparable interface
    @Override
    public int compareTo(Task other) {
        // Compare by arrival time first
        if (this.ArrTime != other.ArrTime) {
            return Integer.compare(this.ArrTime, other.ArrTime);
        }
        // If arrival times are equal, compare by PID
        return this.PID.compareTo(other.PID);
    }
    
    // toString method for easy display
    @Override
    public String toString() {
        return String.format("Task{PID='%s', ArrTime=%d, SrvTime=%d, workAT=%d, workSt=%d}", 
                           PID, ArrTime, SrvTime, workAT, workSt);
    }
    
    // equals and hashCode methods
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Task task = (Task) obj;
        return ArrTime == task.ArrTime &&
               SrvTime == task.SrvTime &&
               workAT == task.workAT &&
               workSt == task.workSt &&
               PID.equals(task.PID);
    }
    
    @Override
    public int hashCode() {
        return java.util.Objects.hash(PID, ArrTime, SrvTime, workAT, workSt);
    }
}
